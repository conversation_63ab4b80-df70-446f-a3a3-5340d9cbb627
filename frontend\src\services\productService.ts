import { apiClient } from '../lib/api';
import type {
  Product,
  CreateProductRequest,
  UpdateProductRequest,
  ProductQueryParams,
  ApiResponse,
  PaginatedResponse,
} from '../types/api';

export class ProductService {
  private static readonly BASE_PATH = '/products';

  // Ürünleri listele
  static async getProducts(params?: ProductQueryParams): Promise<ApiResponse<PaginatedResponse<Product>>> {
    const searchParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
    }

    const queryString = searchParams.toString();
    const endpoint = queryString ? `${this.BASE_PATH}?${queryString}` : this.BASE_PATH;
    
    return apiClient.get<PaginatedResponse<Product>>(endpoint);
  }

  // Tek ürün detayı
  static async getProduct(id: string): Promise<ApiResponse<Product>> {
    return apiClient.get<Product>(`${this.BASE_PATH}/${id}`);
  }

  // Yeni ürün oluştur
  static async createProduct(data: CreateProductRequest): Promise<ApiResponse<Product>> {
    return apiClient.post<Product>(this.BASE_PATH, data);
  }

  // Ürün güncelle
  static async updateProduct(id: string, data: UpdateProductRequest): Promise<ApiResponse<Product>> {
    return apiClient.put<Product>(`${this.BASE_PATH}/${id}`, data);
  }

  // Ürün sil
  static async deleteProduct(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`${this.BASE_PATH}/${id}`);
  }

  // Ürün kodunun benzersizliğini kontrol et
  static async checkCodeUniqueness(code: string, excludeId?: string): Promise<ApiResponse<{ isUnique: boolean }>> {
    const params = new URLSearchParams({ code });
    if (excludeId) {
      params.append('excludeId', excludeId);
    }
    return apiClient.get<{ isUnique: boolean }>(`${this.BASE_PATH}/check-code?${params.toString()}`);
  }

  // Barkodun benzersizliğini kontrol et
  static async checkBarcodeUniqueness(barcode: string, excludeId?: string): Promise<ApiResponse<{ isUnique: boolean }>> {
    const params = new URLSearchParams({ barcode });
    if (excludeId) {
      params.append('excludeId', excludeId);
    }
    return apiClient.get<{ isUnique: boolean }>(`${this.BASE_PATH}/check-barcode?${params.toString()}`);
  }

  // Ürün durumunu değiştir (aktif/pasif)
  static async toggleProductStatus(id: string, active: boolean): Promise<ApiResponse<Product>> {
    return apiClient.patch<Product>(`${this.BASE_PATH}/${id}/status`, { active });
  }

  // Ürün stok durumunu güncelle
  static async updateStock(id: string, stock: number): Promise<ApiResponse<Product>> {
    return apiClient.put<Product>(`${this.BASE_PATH}/${id}/stock`, { stock });
  }

  // Ürün fiyatını güncelle
  static async updatePrice(id: string, basePrice: number): Promise<ApiResponse<Product>> {
    return apiClient.put<Product>(`${this.BASE_PATH}/${id}/price`, { basePrice });
  }

  // Ürün görüntüleme sırasını güncelle
  static async updateDisplayOrder(id: string, displayOrder: number): Promise<ApiResponse<Product>> {
    return apiClient.put<Product>(`${this.BASE_PATH}/${id}/display-order`, { displayOrder });
  }

  // Toplu ürün işlemleri
  static async bulkUpdateStatus(ids: string[], active: boolean): Promise<ApiResponse<{ updated: number }>> {
    return apiClient.put<{ updated: number }>(`${this.BASE_PATH}/bulk/status`, { ids, active });
  }

  static async bulkDelete(ids: string[]): Promise<ApiResponse<{ deleted: number }>> {
    return apiClient.post<{ deleted: number }>(`${this.BASE_PATH}/bulk/delete`, { ids });
  }

  static async bulkUpdateCategory(ids: string[], categoryId: string): Promise<ApiResponse<{ updated: number }>> {
    return apiClient.put<{ updated: number }>(`${this.BASE_PATH}/bulk/category`, { ids, categoryId });
  }

  // Ürün arama önerileri
  static async getSearchSuggestions(query: string): Promise<ApiResponse<string[]>> {
    return apiClient.get<string[]>(`${this.BASE_PATH}/search-suggestions?q=${encodeURIComponent(query)}`);
  }

  // Popüler ürünler
  static async getPopularProducts(limit: number = 10): Promise<ApiResponse<Product[]>> {
    return apiClient.get<Product[]>(`${this.BASE_PATH}/popular?limit=${limit}`);
  }

  // Stok uyarısı olan ürünler
  static async getLowStockProducts(): Promise<ApiResponse<Product[]>> {
    return apiClient.get<Product[]>(`${this.BASE_PATH}/low-stock`);
  }

  // Ürün istatistikleri
  static async getProductStats(): Promise<ApiResponse<{
    total: number;
    active: number;
    inactive: number;
    lowStock: number;
    outOfStock: number;
  }>> {
    return apiClient.get<{
      total: number;
      active: number;
      inactive: number;
      lowStock: number;
      outOfStock: number;
    }>(`${this.BASE_PATH}/stats`);
  }

  // Ürün resmi yükleme
  static async uploadProductImage(file: File): Promise<ApiResponse<{ imageUrl: string }>> {
    const formData = new FormData();
    formData.append('file', file);

    return apiClient.post<{ imageUrl: string }>(`${this.BASE_PATH}/upload-image`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  // Ürün resmi silme
  static async deleteProductImage(imageUrl: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`${this.BASE_PATH}/delete-image`, {
      data: { imageUrl },
    });
  }
}
