// 🚨 Kritik ESLint Kuralları - Bu kuralları ihlal etme!
module.exports = {
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended'
  ],
  rules: {
    // 🔥 ASLA YAPMA
    '@typescript-eslint/no-any': 'error',
    '@typescript-eslint/no-explicit-any': 'error',
    'react-hooks/exhaustive-deps': 'error',
    'react/jsx-key': 'error',
    'no-console': 'warn',
    
    // ✅ HER ZAMAN YAP
    '@typescript-eslint/no-unused-vars': 'error',
    'react/prop-types': 'off', // TypeScript kullanıyoruz
    'react/react-in-jsx-scope': 'off', // React 17+
    
    // 🎯 PERFORMANCE
    'react/jsx-no-bind': 'warn',
    'react-hooks/rules-of-hooks': 'error',
    
    // 🐛 BUG PREVENTION
    'no-duplicate-imports': 'error',
    'no-unreachable': 'error',
    'no-unused-expressions': 'error'
  }
}
