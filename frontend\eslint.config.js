import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import tseslint from 'typescript-eslint'
import { globalIgnores } from 'eslint/config'

export default tseslint.config([
  globalIgnores(['dist']),
  {
    files: ['**/*.{ts,tsx}'],
    extends: [
      js.configs.recommended,
      tseslint.configs.recommended,
      reactHooks.configs['recommended-latest'],
      reactRefresh.configs.vite,
    ],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    rules: {
      // 🚨 SADECE KRİTİK KURALLAR (Makul seviye)
      'react-hooks/rules-of-hooks': 'error', // Hook kuralları
      'react-hooks/exhaustive-deps': 'warn', // Dependency array

      // 🔥 GERÇEKTEN ÖNEMLİ OLANLAR
      '@typescript-eslint/no-unused-vars': 'warn', // <PERSON><PERSON><PERSON><PERSON><PERSON>
      'no-console': 'off', // Console.log'a izin ver (development için)
      'no-unreachable': 'error', // Erişilemeyen kod

      // 🎯 REACT SPECIFIC
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],

      // 🔧 DAHA ESNEK KURALLAR
      '@typescript-eslint/no-explicit-any': 'off', // any'e izin ver (şimdilik)
      'no-duplicate-imports': 'off', // Duplicate import'a izin ver
      '@typescript-eslint/no-empty-object-type': 'off', // Boş interface'lere izin ver
    },
  },
])
