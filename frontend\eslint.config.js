import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import tseslint from 'typescript-eslint'
import { globalIgnores } from 'eslint/config'

export default tseslint.config([
  globalIgnores(['dist']),
  {
    files: ['**/*.{ts,tsx}'],
    extends: [
      js.configs.recommended,
      tseslint.configs.recommended,
      reactHooks.configs['recommended-latest'],
      reactRefresh.configs.vite,
    ],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    rules: {
      // 🚨 KRİTİK KURALLAR - ASLA İHLAL ETME!
      '@typescript-eslint/no-explicit-any': 'error',
      'react-hooks/exhaustive-deps': 'error',

      // 🔥 BUG PREVENTION
      '@typescript-eslint/no-unused-vars': 'error',
      'no-console': 'warn',
      'no-duplicate-imports': 'error',
      'no-unreachable': 'error',

      // ⚡ PERFORMANCE
      'react-hooks/rules-of-hooks': 'error',

      // 🎯 REACT SPECIFIC
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
    },
  },
])
