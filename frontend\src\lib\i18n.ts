import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Türkçe çeviriler
const tr = {
  common: {
    save: 'Kay<PERSON>',
    cancel: '<PERSON>pta<PERSON>',
    delete: 'Sil',
    edit: '<PERSON><PERSON><PERSON><PERSON>',
    add: '<PERSON><PERSON>',
    search: '<PERSON>',
    loading: '<PERSON><PERSON><PERSON><PERSON>yor...',
    error: '<PERSON><PERSON>',
    success: '<PERSON><PERSON>ar<PERSON>lı',
    warning: '<PERSON><PERSON><PERSON>',
    info: '<PERSON><PERSON><PERSON>',
    yes: '<PERSON><PERSON>',
    no: '<PERSON><PERSON><PERSON>',
    close: '<PERSON>pa<PERSON>',
    back: '<PERSON>eri',
    next: '<PERSON><PERSON><PERSON>',
    previous: '<PERSON><PERSON><PERSON>',
    refresh: 'Yenile',
    print: 'Yazdır',
    export: 'Dışa Aktar',
    import: 'İçe Aktar',
    start: 'Ba<PERSON><PERSON>',
    confirm: '<PERSON><PERSON><PERSON>',
    clear: 'Temizle',
    select: '<PERSON><PERSON>',
    selectAll: '<PERSON>ü<PERSON><PERSON><PERSON><PERSON>',
    deselectAll: '<PERSON>ü<PERSON><PERSON><PERSON><PERSON>',
    noData: '<PERSON><PERSON> bulunamadı',
    noResults: '<PERSON><PERSON><PERSON> bulunamadı',
    total: 'Toplam',
    subtotal: 'Ara Toplam',
    required: '<PERSON><PERSON><PERSON><PERSON>',
    optional: 'İsteğe bağlı',
    name: 'Ad',
    description: 'Açıklama',
    date: 'Tarih',
    time: 'Saat',
    status: 'Durum',
    actions: 'İşlemler',
    view: 'Görüntüle',
    show: 'Göster',
    hide: 'Gizle',
    filter: 'Filtrele',
    sort: 'Sırala',
    copy: 'Kopyala',
    upload: 'Yükle',
    download: 'İndir',
    submit: 'Gönder',
    reset: 'Sıfırla',
    apply: 'Uygula',
    remove: 'Kaldır',
    update: 'Güncelle',
    create: 'Oluştur',
    details: 'Detaylar',
    active: 'Aktif',
    inactive: 'Pasif',
    enabled: 'Etkin',
    disabled: 'Devre Dışı',
    pending: 'Beklemede',
    approved: 'Onaylandı',
    completed: 'Tamamlandı',
    inProgress: 'Devam Ediyor',
    cancelled: 'İptal Edildi',
    available: 'Mevcut',
    unavailable: 'Mevcut Değil',
    showing: 'Gösteriliyor',
    of: 'toplam',
    change: 'Değiştir',
    dragDropOrClick: 'Dosyayı sürükleyip bırakın veya tıklayın',
    uploading: 'Yükleniyor',
  },
  auth: {
    login: 'Giriş Yap',
    logout: 'Çıkış Yap',
    username: 'Kullanıcı Adı',
    password: 'Şifre',
    forgotPassword: 'Şifremi Unuttum',
    rememberMe: 'Beni Hatırla',
    loginError: 'Giriş başarısız',
    invalidCredentials: 'Geçersiz kullanıcı adı veya şifre',
  },
  navigation: {
    dashboard: 'Ana Sayfa',
    home: 'Ana Sayfa',
    orders: 'Siparişler',
    tables: 'Masalar',
    products: 'Ürünler',
    categories: 'Kategoriler',
    customers: 'Müşteriler',
    reports: 'Raporlar',
    settings: 'Ayarlar',
    kitchen: 'Mutfak',
    cashier: 'Kasa',
    profile: 'Profil',
    more: 'Daha Fazla',
  },
  dashboard: {
    welcome: 'Hoş Geldiniz',
    goodMorning: 'Günaydın',
    bestService: 'Müşterilerinize en iyi hizmeti verin',
    systemRunning: 'Atropos POS Sistemi başarıyla çalışıyor.',
    todaysOrders: 'Bugünkü Siparişler',
    dailyRevenue: 'Günlük Ciro',
    activeTables: 'Aktif Masalar',
    pendingOrders: 'Bekleyen Siparişler',
    totalEarning: 'Toplam Kazanç',
    inProgress: 'Devam Eden',
    waitingList: 'Bekleme Listesi',
    waitingPayment: 'Ödeme Bekliyor',
    popularDishes: 'Popüler Yemekler',
    outOfStock: 'Stok Tükendi',
    viewAll: 'Tümünü Gör',
    noDishesToday: 'Bugün henüz hiç yemek satılmadı.',
    allStockReady: 'Tüm stok ürünleri şu anda hazır!',
    noOrders: 'Hiç Siparişiniz Yok',
    noOrdersToday: 'Bugün müşterilerinizden henüz sipariş almadınız.',
    createNewOrder: 'Yeni Sipariş Oluştur',
    searchOrder: 'Sipariş Ara',
    thanYesterday: 'dünden',
    appName: 'Bitepoint POS',
    restaurantSystem: 'Restoran Sistemi',
    increase: 'artış',
    occupancy: 'doluluk',
    inKitchen: 'Mutfakta',
    quickOrderCreate: 'Hızlı sipariş oluştur',
    viewTableLayout: 'Masa düzenini görüntüle',
    reviewSalesReports: 'Satış raporlarını incele',
    view: 'Görüntüle',
    setupSuccessful: 'Kurulum Başarılı!',
    frontendTech: 'Frontend Teknolojileri',
    backendTech: 'Backend Teknolojileri',
    multiLanguage: 'Çoklu Dil',
    themeTest: 'Tema Testi',
    start: 'Başlat',
  },
  pos: {
    newOrder: 'Yeni Sipariş',
    addToCart: 'Sepete Ekle',
    cart: 'Sepet',
    total: 'Toplam',
    subtotal: 'Ara Toplam',
    tax: 'KDV',
    discount: 'İndirim',
    payment: 'Ödeme',
    cash: 'Nakit',
    creditCard: 'Kredi Kartı',
    table: 'Masa',
    customer: 'Müşteri',
    note: 'Not',
    quantity: 'Adet',
    price: 'Fiyat',
  },
  theme: {
    light: 'Açık Tema',
    dark: 'Koyu Tema',
    toggleTheme: 'Tema Değiştir',
  },
  language: {
    turkish: 'Türkçe',
    english: 'English',
    changeLanguage: 'Dil Değiştir',
  },
  roles: {
    cashier: 'Kasiyer',
    admin: 'Yönetici',
    manager: 'Müdür',
    waiter: 'Garson',
  },
  products: {
    title: 'Ürün Yönetimi',
    subtitle: 'Ürünlerinizi yönetin ve düzenleyin',
    addProduct: 'Ürün Ekle',
    editProduct: 'Ürün Düzenle',
    deleteProduct: 'Ürün Sil',
    productList: 'Ürün Listesi',
    productDetails: 'Ürün Detayları',
    searchProducts: 'Ürün Ara...',
    filterByCategory: 'Kategoriye Göre Filtrele',
    allCategories: 'Tüm Kategoriler',
    noProductsFound: 'Ürün bulunamadı',
    productAdded: 'Ürün başarıyla eklendi',
    productUpdated: 'Ürün başarıyla güncellendi',
    productDeleted: 'Ürün başarıyla silindi',
    confirmDelete: 'Bu ürünü silmek istediğinizden emin misiniz?',
    deleteConfirmation: 'Silme Onayı',
    // Form alanları
    productName: 'Ürün Adı',
    productCode: 'Ürün Kodu',
    barcode: 'Barkod',
    description: 'Açıklama',
    category: 'Kategori',
    price: 'Fiyat',
    cost: 'Maliyet',
    tax: 'Vergi',
    stock: 'Stok',
    minStock: 'Minimum Stok',
    unit: 'Birim',
    status: 'Durum',
    active: 'Aktif',
    inactive: 'Pasif',
    image: 'Görsel',
    productImage: 'Ürün Resmi',
    // Validasyon mesajları
    nameRequired: 'Ürün adı zorunludur',
    codeRequired: 'Ürün kodu zorunludur',
    priceRequired: 'Fiyat zorunludur',
    categoryRequired: 'Kategori seçimi zorunludur',
    taxRequired: 'Vergi seçimi zorunludur',
    priceInvalid: 'Geçerli bir fiyat giriniz',
    stockInvalid: 'Geçerli bir stok miktarı giriniz',
    // Tablo başlıkları
    actions: 'İşlemler',
    createdAt: 'Oluşturma Tarihi',
    updatedAt: 'Güncelleme Tarihi',
    // Form bölümleri
    basicInfo: 'Temel Bilgiler',
    categoryAndTax: 'Kategori ve Vergi',
    priceInfo: 'Fiyat Bilgileri',
    stockInfo: 'Stok Bilgileri',
    statusSettings: 'Durum Ayarları',
    // Ek alanlar
    selectCategory: 'Kategori Seçiniz',
    selectTax: 'Vergi Seçiniz',
    shortDescription: 'Kısa Açıklama',
    profitMargin: 'Kar Marjı (%)',
    finalPrice: 'Son Fiyat',
    trackStock: 'Stok Takibi Yap',
    sellable: 'Satılabilir',
    showInMenu: 'Menüde Göster',
    noTracking: 'Takip Yok',
    // Birimler
    units: {
      piece: 'Adet',
      kg: 'Kilogram',
      gram: 'Gram',
      liter: 'Litre',
      ml: 'Mililitre',
      meter: 'Metre',
      cm: 'Santimetre',
      package: 'Paket',
      box: 'Kutu',
    },
    // Hata mesajları
    createError: 'Ürün oluşturulurken hata oluştu',
    updateError: 'Ürün güncellenirken hata oluştu',
    deleteError: 'Ürün silinirken hata oluştu',
    statusUpdated: 'Ürün durumu güncellendi',
    statusUpdateError: 'Ürün durumu güncellenirken hata oluştu',
  },
  more: {
    tools: 'Araçlar',
    help: 'Yardım',
    outletInformation: 'Outlet Bilgileri',
    reportSummary: 'Rapor Özeti',
    productManagement: 'Ürün Yönetimi',
    employeeManagement: 'Çalışan Yönetimi',
    settings: 'Ayarlar',
    language: 'Dil',
    helpCenter: 'Yardım Merkezi',
    privacyPolicy: 'Gizlilik Politikası',
    appInformation: 'Uygulama Bilgileri',
  },
  categories: {
    title: 'Kategori Yönetimi',
    subtitle: 'Kategorilerinizi yönetin ve düzenleyin',
    addCategory: 'Kategori Ekle',
    editCategory: 'Kategori Düzenle',
    deleteCategory: 'Kategori Sil',
    categoryList: 'Kategori Listesi',
    categoryDetails: 'Kategori Detayları',
    searchCategories: 'Kategori Ara...',
    noCategoriesFound: 'Kategori bulunamadı',
    categoryAdded: 'Kategori başarıyla eklendi',
    categoryUpdated: 'Kategori başarıyla güncellendi',
    categoryDeleted: 'Kategori başarıyla silindi',
    confirmDelete: 'Bu kategoriyi silmek istediğinizden emin misiniz?',
    deleteConfirmation: 'Silme Onayı',
    // Form alanları
    categoryName: 'Kategori Adı',
    parentCategory: 'Üst Kategori',
    categoryColor: 'Kategori Rengi',
    categoryIcon: 'Kategori İkonu',
    description: 'Açıklama',
    image: 'Resim',
    showInKitchen: 'Mutfakta Göster',
    showInMenu: 'Menüde Göster',
    preparationTime: 'Hazırlık Süresi (dk)',
    displayOrder: 'Görüntüleme Sırası',
    active: 'Aktif',
    inactive: 'Pasif',
    color: 'Renk',
    icon: 'İkon',
    noParent: 'Ana Kategori',
    // Validation mesajları
    nameRequired: 'Kategori adı zorunludur',
    nameMinLength: 'Kategori adı en az 2 karakter olmalıdır',
    nameMaxLength: 'Kategori adı en fazla 100 karakter olabilir',
    colorInvalid: 'Geçersiz hex renk formatı (#RRGGBB)',
    preparationTimeInvalid: 'Hazırlık süresi geçerli bir sayı olmalıdır',
    displayOrderInvalid: 'Görüntüleme sırası geçerli bir sayı olmalıdır',
    // Tablo başlıkları
    actions: 'İşlemler',
    createdAt: 'Oluşturulma Tarihi',
    updatedAt: 'Güncellenme Tarihi',
    productCount: 'Ürün Sayısı',
    // Form bölümleri
    basicInfo: 'Temel Bilgiler',
    displaySettings: 'Görüntüleme Ayarları',
    kitchenSettings: 'Mutfak Ayarları',
    // Ek alanlar
    selectParentCategory: 'Üst Kategori Seç',
    noParentCategory: 'Ana Kategori',
    rootCategory: 'Ana Kategori',
    subCategory: 'Alt Kategori',
    // Hata mesajları
    createError: 'Kategori oluşturulurken hata oluştu',
    updateError: 'Kategori güncellenirken hata oluştu',
    deleteError: 'Kategori silinirken hata oluştu',
    statusUpdated: 'Kategori durumu güncellendi',
    statusUpdateError: 'Kategori durumu güncellenirken hata oluştu',
  },
  taxes: {
    title: 'Vergi Yönetimi',
    subtitle: 'Vergi oranlarınızı yönetin ve düzenleyin',
    addTax: 'Vergi Ekle',
    editTax: 'Vergi Düzenle',
    deleteTax: 'Vergi Sil',
    taxList: 'Vergi Listesi',
    taxDetails: 'Vergi Detayları',
    searchTaxes: 'Vergi Ara...',
    noTaxesFound: 'Vergi bulunamadı',
    taxAdded: 'Vergi başarıyla eklendi',
    taxUpdated: 'Vergi başarıyla güncellendi',
    taxDeleted: 'Vergi başarıyla silindi',
    confirmDelete: 'Bu vergiyi silmek istediğinizden emin misiniz?',
    deleteConfirmation: 'Silme Onayı',
    // Form alanları
    taxName: 'Vergi Adı',
    taxCode: 'Vergi Kodu',
    taxRate: 'Vergi Oranı (%)',
    taxType: 'Vergi Tipi',
    isDefault: 'Varsayılan Vergi',
    isIncluded: 'Fiyata Dahil',
    inclusive: 'Dahil',
    exclusive: 'Hariç',
    active: 'Aktif',
    inactive: 'Pasif',
    // Vergi tipleri
    taxTypes: {
      VAT: 'KDV',
      OTV: 'ÖTV',
      OIV: 'ÖİV',
      DAMGA: 'Damga Vergisi',
    },
    // Validation mesajları
    nameRequired: 'Vergi adı zorunludur',
    nameMinLength: 'Vergi adı en az 2 karakter olmalıdır',
    nameMaxLength: 'Vergi adı en fazla 100 karakter olabilir',
    codeRequired: 'Vergi kodu zorunludur',
    codeMaxLength: 'Vergi kodu en fazla 20 karakter olabilir',
    rateRequired: 'Vergi oranı zorunludur',
    rateMin: 'Vergi oranı 0\'dan küçük olamaz',
    rateMax: 'Vergi oranı %100\'den fazla olamaz',
    typeRequired: 'Vergi tipi seçimi zorunludur',
    // Tablo başlıkları
    actions: 'İşlemler',
    createdAt: 'Oluşturulma Tarihi',
    updatedAt: 'Güncellenme Tarihi',
    productCount: 'Ürün Sayısı',
    // Form bölümleri
    basicInfo: 'Temel Bilgiler',
    rateSettings: 'Oran Ayarları',
    statusSettings: 'Durum Ayarları',
    // Ek alanlar
    selectTaxType: 'Vergi Tipi Seç',
    defaultTaxInfo: 'Bu vergi varsayılan vergi olarak ayarlanacak',
    includedTaxInfo: 'Bu vergi ürün fiyatına dahil edilecek',
    // Hata mesajları
    createError: 'Vergi oluşturulurken hata oluştu',
    updateError: 'Vergi güncellenirken hata oluştu',
    deleteError: 'Vergi silinirken hata oluştu',
    statusUpdated: 'Vergi durumu güncellendi',
    statusUpdateError: 'Vergi durumu güncellenirken hata oluştu',
    codeExists: 'Bu vergi kodu zaten kullanılıyor',
  },
};

// İngilizce çeviriler
const en = {
  common: {
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Info',
    yes: 'Yes',
    no: 'No',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    refresh: 'Refresh',
    print: 'Print',
    export: 'Export',
    import: 'Import',
    start: 'Start',
    confirm: 'Confirm',
    clear: 'Clear',
    select: 'Select',
    selectAll: 'Select All',
    deselectAll: 'Deselect All',
    noData: 'No data found',
    noResults: 'No results found',
    total: 'Total',
    subtotal: 'Subtotal',
    required: 'Required',
    optional: 'Optional',
    name: 'Name',
    description: 'Description',
    date: 'Date',
    time: 'Time',
    status: 'Status',
    actions: 'Actions',
    view: 'View',
    show: 'Show',
    hide: 'Hide',
    filter: 'Filter',
    sort: 'Sort',
    copy: 'Copy',
    upload: 'Upload',
    download: 'Download',
    submit: 'Submit',
    reset: 'Reset',
    apply: 'Apply',
    remove: 'Remove',
    update: 'Update',
    create: 'Create',
    details: 'Details',
    active: 'Active',
    inactive: 'Inactive',
    enabled: 'Enabled',
    disabled: 'Disabled',
    pending: 'Pending',
    approved: 'Approved',
    completed: 'Completed',
    inProgress: 'In Progress',
    cancelled: 'Cancelled',
    available: 'Available',
    unavailable: 'Unavailable',
    showing: 'Showing',
    of: 'of',
    change: 'Change',
    dragDropOrClick: 'Drag and drop or click to select',
    uploading: 'Uploading',
  },
  auth: {
    login: 'Login',
    logout: 'Logout',
    username: 'Username',
    password: 'Password',
    forgotPassword: 'Forgot Password',
    rememberMe: 'Remember Me',
    loginError: 'Login failed',
    invalidCredentials: 'Invalid username or password',
  },
  navigation: {
    dashboard: 'Dashboard',
    home: 'Home',
    orders: 'Orders',
    tables: 'Tables',
    products: 'Products',
    categories: 'Categories',
    customers: 'Customers',
    reports: 'Reports',
    settings: 'Settings',
    kitchen: 'Kitchen',
    cashier: 'Cashier',
    profile: 'Profile',
    more: 'More',
  },
  dashboard: {
    welcome: 'Welcome',
    goodMorning: 'Good Morning',
    bestService: 'Give your best services for customers',
    systemRunning: 'Atropos POS System is running successfully.',
    todaysOrders: 'Today\'s Orders',
    dailyRevenue: 'Daily Revenue',
    activeTables: 'Active Tables',
    pendingOrders: 'Pending Orders',
    totalEarning: 'Total Earning',
    inProgress: 'In Progress',
    waitingList: 'Waiting List',
    waitingPayment: 'Waiting for Payment',
    popularDishes: 'Popular Dishes',
    outOfStock: 'Out of Stock',
    viewAll: 'View All',
    noDishesToday: 'No dishes have been sold yet today.',
    allStockReady: 'All stock items are ready now!',
    noOrders: 'You Don\'t Have Any Orders',
    noOrdersToday: 'You haven\'t received any orders from your customers today.',
    createNewOrder: 'Create New Order',
    searchOrder: 'Search a Order',
    thanYesterday: 'than yesterday',
    appName: 'Bitepoint POS',
    restaurantSystem: 'Restaurant System',
    increase: 'increase',
    occupancy: 'occupancy',
    inKitchen: 'In Kitchen',
    quickOrderCreate: 'Create quick order',
    viewTableLayout: 'View table layout',
    reviewSalesReports: 'Review sales reports',
    view: 'View',
    setupSuccessful: 'Setup Successful!',
    frontendTech: 'Frontend Technologies',
    backendTech: 'Backend Technologies',
    multiLanguage: 'Multi-Language',
    themeTest: 'Theme Test',
    start: 'Start',
  },
  pos: {
    newOrder: 'New Order',
    addToCart: 'Add to Cart',
    cart: 'Cart',
    total: 'Total',
    subtotal: 'Subtotal',
    tax: 'Tax',
    discount: 'Discount',
    payment: 'Payment',
    cash: 'Cash',
    creditCard: 'Credit Card',
    table: 'Table',
    customer: 'Customer',
    note: 'Note',
    quantity: 'Quantity',
    price: 'Price',
  },
  theme: {
    light: 'Light Theme',
    dark: 'Dark Theme',
    toggleTheme: 'Toggle Theme',
  },
  language: {
    turkish: 'Türkçe',
    english: 'English',
    changeLanguage: 'Change Language',
  },
  roles: {
    cashier: 'Cashier',
    admin: 'Admin',
    manager: 'Manager',
    waiter: 'Waiter',
  },
  products: {
    title: 'Product Management',
    subtitle: 'Manage and organize your products',
    addProduct: 'Add Product',
    editProduct: 'Edit Product',
    deleteProduct: 'Delete Product',
    productList: 'Product List',
    productDetails: 'Product Details',
    searchProducts: 'Search Products...',
    filterByCategory: 'Filter by Category',
    allCategories: 'All Categories',
    noProductsFound: 'No products found',
    productAdded: 'Product added successfully',
    productUpdated: 'Product updated successfully',
    productDeleted: 'Product deleted successfully',
    confirmDelete: 'Are you sure you want to delete this product?',
    deleteConfirmation: 'Delete Confirmation',
    // Form fields
    productName: 'Product Name',
    productCode: 'Product Code',
    barcode: 'Barcode',
    description: 'Description',
    category: 'Category',
    price: 'Price',
    cost: 'Cost',
    tax: 'Tax',
    stock: 'Stock',
    minStock: 'Minimum Stock',
    unit: 'Unit',
    status: 'Status',
    active: 'Active',
    inactive: 'Inactive',
    image: 'Image',
    productImage: 'Product Image',
    // Validation messages
    nameRequired: 'Product name is required',
    codeRequired: 'Product code is required',
    priceRequired: 'Price is required',
    categoryRequired: 'Category selection is required',
    taxRequired: 'Tax selection is required',
    priceInvalid: 'Please enter a valid price',
    stockInvalid: 'Please enter a valid stock amount',
    // Table headers
    actions: 'Actions',
    createdAt: 'Created At',
    updatedAt: 'Updated At',
    // Form sections
    basicInfo: 'Basic Information',
    categoryAndTax: 'Category and Tax',
    priceInfo: 'Price Information',
    stockInfo: 'Stock Information',
    statusSettings: 'Status Settings',
    // Additional fields
    selectCategory: 'Select Category',
    selectTax: 'Select Tax',
    shortDescription: 'Short Description',
    profitMargin: 'Profit Margin (%)',
    finalPrice: 'Final Price',
    trackStock: 'Track Stock',
    sellable: 'Sellable',
    showInMenu: 'Show in Menu',
    noTracking: 'No Tracking',
    // Units
    units: {
      piece: 'Piece',
      kg: 'Kilogram',
      gram: 'Gram',
      liter: 'Liter',
      ml: 'Milliliter',
      meter: 'Meter',
      cm: 'Centimeter',
      package: 'Package',
      box: 'Box',
    },
    // Error messages
    createError: 'Error creating product',
    updateError: 'Error updating product',
    deleteError: 'Error deleting product',
    statusUpdated: 'Product status updated',
    statusUpdateError: 'Error updating product status',
  },
  more: {
    tools: 'Tools',
    help: 'Help',
    outletInformation: 'Outlet Information',
    reportSummary: 'Report Summary',
    productManagement: 'Product Management',
    employeeManagement: 'Employee Management',
    settings: 'Settings',
    language: 'Language',
    helpCenter: 'Help Center',
    privacyPolicy: 'Privacy Policy',
    appInformation: 'App Information',
  },
  categories: {
    title: 'Category Management',
    subtitle: 'Manage and organize your categories',
    addCategory: 'Add Category',
    editCategory: 'Edit Category',
    deleteCategory: 'Delete Category',
    categoryList: 'Category List',
    categoryDetails: 'Category Details',
    searchCategories: 'Search Categories...',
    noCategoriesFound: 'No categories found',
    categoryAdded: 'Category added successfully',
    categoryUpdated: 'Category updated successfully',
    categoryDeleted: 'Category deleted successfully',
    confirmDelete: 'Are you sure you want to delete this category?',
    deleteConfirmation: 'Delete Confirmation',
    // Form fields
    categoryName: 'Category Name',
    parentCategory: 'Parent Category',
    categoryColor: 'Category Color',
    categoryIcon: 'Category Icon',
    description: 'Description',
    image: 'Image',
    showInKitchen: 'Show in Kitchen',
    showInMenu: 'Show in Menu',
    preparationTime: 'Preparation Time (min)',
    displayOrder: 'Display Order',
    active: 'Active',
    inactive: 'Inactive',
    color: 'Color',
    icon: 'Icon',
    noParent: 'Main Category',
    // Validation messages
    nameRequired: 'Category name is required',
    nameMinLength: 'Category name must be at least 2 characters',
    nameMaxLength: 'Category name cannot exceed 100 characters',
    colorInvalid: 'Invalid hex color format (#RRGGBB)',
    preparationTimeInvalid: 'Preparation time must be a valid number',
    displayOrderInvalid: 'Display order must be a valid number',
    // Table headers
    actions: 'Actions',
    createdAt: 'Created At',
    updatedAt: 'Updated At',
    productCount: 'Product Count',
    // Form sections
    basicInfo: 'Basic Information',
    displaySettings: 'Display Settings',
    kitchenSettings: 'Kitchen Settings',
    // Additional fields
    selectParentCategory: 'Select Parent Category',
    noParentCategory: 'Main Category',
    rootCategory: 'Root Category',
    subCategory: 'Sub Category',
    // Error messages
    createError: 'Error creating category',
    updateError: 'Error updating category',
    deleteError: 'Error deleting category',
    statusUpdated: 'Category status updated',
    statusUpdateError: 'Error updating category status',
  },
  taxes: {
    title: 'Tax Management',
    subtitle: 'Manage and organize your tax rates',
    addTax: 'Add Tax',
    editTax: 'Edit Tax',
    deleteTax: 'Delete Tax',
    taxList: 'Tax List',
    taxDetails: 'Tax Details',
    searchTaxes: 'Search Taxes...',
    noTaxesFound: 'No taxes found',
    taxAdded: 'Tax added successfully',
    taxUpdated: 'Tax updated successfully',
    taxDeleted: 'Tax deleted successfully',
    confirmDelete: 'Are you sure you want to delete this tax?',
    deleteConfirmation: 'Delete Confirmation',
    // Form fields
    taxName: 'Tax Name',
    taxCode: 'Tax Code',
    taxRate: 'Tax Rate (%)',
    taxType: 'Tax Type',
    isDefault: 'Default Tax',
    isIncluded: 'Included in Price',
    inclusive: 'Inclusive',
    exclusive: 'Exclusive',
    active: 'Active',
    inactive: 'Inactive',
    // Tax types
    taxTypes: {
      VAT: 'VAT',
      OTV: 'Special Consumption Tax',
      OIV: 'Special Transaction Tax',
      DAMGA: 'Stamp Tax',
    },
    // Validation messages
    nameRequired: 'Tax name is required',
    nameMinLength: 'Tax name must be at least 2 characters',
    nameMaxLength: 'Tax name cannot exceed 100 characters',
    codeRequired: 'Tax code is required',
    codeMaxLength: 'Tax code cannot exceed 20 characters',
    rateRequired: 'Tax rate is required',
    rateMin: 'Tax rate cannot be negative',
    rateMax: 'Tax rate cannot exceed 100%',
    typeRequired: 'Tax type selection is required',
    // Table headers
    actions: 'Actions',
    createdAt: 'Created At',
    updatedAt: 'Updated At',
    productCount: 'Product Count',
    // Form sections
    basicInfo: 'Basic Information',
    rateSettings: 'Rate Settings',
    statusSettings: 'Status Settings',
    // Additional fields
    selectTaxType: 'Select Tax Type',
    defaultTaxInfo: 'This tax will be set as default',
    includedTaxInfo: 'This tax will be included in product price',
    // Error messages
    createError: 'Error creating tax',
    updateError: 'Error updating tax',
    deleteError: 'Error deleting tax',
    statusUpdated: 'Tax status updated',
    statusUpdateError: 'Error updating tax status',
    codeExists: 'This tax code already exists',
  },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources: {
      tr: { translation: tr },
      en: { translation: en },
    },
    fallbackLng: 'tr',
    debug: false,
    interpolation: {
      escapeValue: false,
    },
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    },
  });

export default i18n;
