import js from "@eslint/js";
import globals from "globals";
import tseslint from "typescript-eslint";
import pluginReact from "eslint-plugin-react";
import reactHooks from "eslint-plugin-react-hooks";

export default [
  // Global ignores
  {
    ignores: ["**/dist/**", "**/node_modules/**", "**/build/**"]
  },

  // Frontend files
  {
    files: ["frontend/**/*.{js,jsx,ts,tsx}"],
    languageOptions: {
      globals: globals.browser,
      parser: tseslint.parser,
      parserOptions: {
        ecmaVersion: 2020,
        sourceType: "module",
        ecmaFeatures: { jsx: true }
      }
    },
    plugins: {
      "react-hooks": reactHooks,
      "react": pluginReact,
      "@typescript-eslint": tseslint.plugin
    },
    rules: {
      // 🚨 SADECE KRİTİK KURALLAR
      "react-hooks/rules-of-hooks": "error",
      "react-hooks/exhaustive-deps": "warn",

      // 🔥 MAKUL KURALLAR
      "@typescript-eslint/no-unused-vars": "warn",
      "no-console": "off", // Development için izin ver
      "no-unreachable": "error",

      // 🎯 REACT SPECIFIC
      "react/react-in-jsx-scope": "off", // React 17+
      "react/prop-types": "off", // TypeScript kullanıyoruz

      // 🔧 ESNEK KURALLAR
      "@typescript-eslint/no-explicit-any": "off",
    }
  },

  // Backend files
  {
    files: ["backend/**/*.{js,ts}"],
    languageOptions: {
      globals: globals.node,
      parser: tseslint.parser,
      parserOptions: {
        ecmaVersion: 2020,
        sourceType: "module"
      }
    },
    plugins: {
      "@typescript-eslint": tseslint.plugin
    },
    rules: {
      "@typescript-eslint/no-unused-vars": "warn",
      "no-console": "off", // Backend'de console.log normal
      "no-unreachable": "error"
    }
  }
];
